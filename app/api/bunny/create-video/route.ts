import { createSupabaseServerClient } from "@/lib/supabase/client"
import { NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const { title, postId } = await request.json()

    if (!title || !postId) {
      return NextResponse.json(
        { error: "Title and postId are required" },
        { status: 400 }
      )
    }

    // Check environment variables
    if (!process.env.BUNNY_LIBRARY_ID || !process.env.BUNNY_API_KEY) {
      console.error('Missing Bunny.net environment variables:', {
        hasLibraryId: !!process.env.BUNNY_LIBRARY_ID,
        hasApiKey: !!process.env.BUNNY_API_KEY
      })
      return NextResponse.json(
        { error: "Bunny.net configuration missing" },
        { status: 500 }
      )
    }

    const supabase = await createSupabaseServerClient()
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Create video in Bunny.net
    const response = await fetch(`https://video.bunnycdn.com/library/${process.env.BUNNY_LIBRARY_ID}/videos`, {
      method: 'POST',
      headers: {
        'AccessKey': process.env.BUNNY_API_KEY!,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        title: title,
      }),
    })

    if (!response.ok) {
      const error = await response.text()
      console.error('Bunny API error:', error)
      return NextResponse.json(
        { error: "Failed to create video in Bunny.net" },
        { status: 500 }
      )
    }

    const bunnyData = await response.json()
    
    // Save video record to Supabase
    const { data: video, error: dbError } = await supabase
      .from("videos")
      .insert({
        post_id: postId,
        creator_id: user.id,
        bunny_video_id: bunnyData.guid,
        title: title,
        is_free: false,
        encoding_status: 'Queued'
      })
      .select()
      .single()

    if (dbError) {
      console.error('Database error:', dbError)
      return NextResponse.json(
        { error: "Failed to save video record" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      videoId: bunnyData.guid,
      uploadUrl: `https://video.bunnycdn.com/library/${process.env.BUNNY_LIBRARY_ID}/videos/${bunnyData.guid}`,
      video: video,
      bunnyResponse: bunnyData // Debug: see what Bunny returns
    })

  } catch (error) {
    console.error("Error creating video:", error)
    return NextResponse.json(
      { error: "Failed to create video" },
      { status: 500 }
    )
  }
}
