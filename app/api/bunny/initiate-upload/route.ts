import { createServerSupabaseClient } from "@/lib/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const { title, postId } = await request.json();

    if (!title || !postId) {
      return NextResponse.json(
        { error: "Title and postId are required" },
        { status: 400 }
      );
    }

    if (!process.env.BUNNY_LIBRARY_ID || !process.env.BUNNY_API_KEY) {
      console.error("Missing Bunny.net environment variables");
      return NextResponse.json(
        { error: "Bunny.net configuration missing" },
        { status: 500 }
      );
    }

    const supabase = await createServerSupabaseClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // 1. Create a video object in Bunny.net
    const createVideoResponse = await fetch(
      `https://video.bunnycdn.com/library/${process.env.BUNNY_LIBRARY_ID}/videos`,
      {
        method: "POST",
        headers: {
          AccessKey: process.env.BUNNY_API_KEY!,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ title }),
      }
    );

    if (!createVideoResponse.ok) {
      const errorText = await createVideoResponse.text();
      console.error("Bunny API error (create video):", {
        status: createVideoResponse.status,
        statusText: createVideoResponse.statusText,
        error: errorText,
      });
      return NextResponse.json(
        { error: "Failed to create video in Bunny.net", details: errorText },
        { status: 500 }
      );
    }

    const videoData = await createVideoResponse.json();

    // 2. Create a short-lived token for the upload
    const createTokenResponse = await fetch(
      `https://video.bunnycdn.com/library/${process.env.BUNNY_LIBRARY_ID}/videos/${videoData.guid}/token`,
      {
        method: "POST",
        headers: {
          AccessKey: process.env.BUNNY_API_KEY!,
        },
      }
    );

    if (!createTokenResponse.ok) {
      const errorText = await createTokenResponse.text();
      console.error("Bunny API error (create token):", {
        status: createTokenResponse.status,
        statusText: createTokenResponse.statusText,
        error: errorText,
      });
      return NextResponse.json(
        { error: "Failed to create upload token", details: errorText },
        { status: 500 }
      );
    }

    const tokenData = await createTokenResponse.json();

    // 3. Save video record to Supabase
    const { data: video, error: dbError } = await supabase
      .from("videos")
      .insert({
        post_id: postId,
        creator_id: user.id,
        bunny_video_id: videoData.guid,
        title: title,
        is_free: false,
        encoding_status: "Queued",
      })
      .select()
      .single();

    if (dbError) {
      console.error("Database error:", dbError);
      // Optional: Attempt to delete the video from Bunny.net to avoid orphans
      await fetch(
        `https://video.bunnycdn.com/library/${process.env.BUNNY_LIBRARY_ID}/videos/${videoData.guid}`,
        {
          method: "DELETE",
          headers: {
            AccessKey: process.env.BUNNY_API_KEY!,
          },
        }
      );
      return NextResponse.json(
        { error: "Failed to save video record" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      videoId: videoData.guid,
      token: tokenData.token,
      video: video,
    });
  } catch (error) {
    console.error("Error initiating video upload:", error);
    return NextResponse.json(
      { error: "Failed to initiate video upload" },
      { status: 500 }
    );
  }
}
