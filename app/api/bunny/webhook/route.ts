import { createSupabaseServerClient } from "@/lib/supabase/client"
import { NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { VideoLibraryId, VideoGuid, Status } = body

    // Log webhook for debugging
    console.log(`[BUNNY WEBHOOK] Library: ${VideoLibraryId} | Video: ${VideoGuid} | Status: ${Status}`)

    if (!VideoGuid) {
      return NextResponse.json(
        { error: "Video GUID is required" },
        { status: 400 }
      )
    }

    // Convert status number to string
    const statusMap: { [key: number]: string } = {
      0: 'Queued',
      1: 'Processing',
      2: 'Encoding',
      3: 'Ready',        // Finished
      4: 'Ready',        // Resolution finished (playable)
      5: 'Failed',
      6: 'UploadStarted',
      7: 'UploadFinished',
      8: 'UploadFailed',
      9: 'CaptionsGenerated',
      10: 'TitleGenerated'
    }

    const encodingStatus = statusMap[Status] || 'Unknown'

    const supabase = await createSupabaseServerClient()

    // Update video status in database
    const { error } = await supabase
      .from("videos")
      .update({
        encoding_status: encodingStatus,
        updated_at: new Date().toISOString()
      })
      .eq("bunny_video_id", VideoGuid)

    if (error) {
      console.error("Failed to update video status:", error)
      return NextResponse.json(
        { error: "Database update failed" },
        { status: 500 }
      )
    }

    console.log(`[BUNNY WEBHOOK] Successfully updated video ${VideoGuid} to status: ${encodingStatus}`)

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error("Webhook error:", error)
    return NextResponse.json(
      { error: "Webhook processing failed" },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Bunny.net Webhook Endpoint',
    status: 'active'
  })
}
