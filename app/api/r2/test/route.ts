import { NextRequest, NextResponse } from "next/server";
import r2Client from "@/lib/r2-client";
import { ListObjectsV2Command } from "@aws-sdk/client-s3";

export async function GET(request: NextRequest) {
  try {
    // Check if R2 is properly configured
    if (!process.env.CLOUDFLARE_R2_BUCKET_NAME || !process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || !process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || !process.env.CLOUDFLARE_R2_S3_ENDPOINT) {
      return NextResponse.json({
        success: false,
        error: "R2 configuration missing",
        config: {
          hasBucket: !!process.env.CLOUDFLARE_R2_BUCKET_NAME,
          hasAccessKey: !!process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
          hasSecretKey: !!process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
          hasEndpoint: !!process.env.CLOUDFLARE_R2_S3_ENDPOINT,
          hasPublicUrl: !!process.env.CLOUDFLARE_R2_PUBLIC_URL
        }
      }, { status: 500 });
    }

    // Test connection by listing objects
    const command = new ListObjectsV2Command({
      Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
      MaxKeys: 5
    });

    const response = await r2Client.send(command);

    return NextResponse.json({
      success: true,
      message: "R2 connection successful",
      config: {
        bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
        endpoint: process.env.CLOUDFLARE_R2_S3_ENDPOINT,
        publicUrl: process.env.CLOUDFLARE_R2_PUBLIC_URL
      },
      stats: {
        objectCount: response.Contents?.length || 0,
        sampleObjects: response.Contents?.slice(0, 3).map(obj => ({
          key: obj.Key,
          size: obj.Size,
          lastModified: obj.LastModified
        })) || []
      }
    });

  } catch (error) {
    console.error("R2 test error:", error);
    return NextResponse.json({
      success: false,
      error: "R2 connection failed",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
