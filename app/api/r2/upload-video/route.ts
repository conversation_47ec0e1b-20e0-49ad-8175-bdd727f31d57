import { NextRequest, NextResponse } from "next/server";
import { createSupabaseServerClient } from "@/lib/supabase/client";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const postId = formData.get('postId') as string;

    if (!file || !postId) {
      return NextResponse.json(
        { error: "File and postId are required" },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedExtensions = ['.mp4', '.mov'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
    if (!allowedExtensions.includes(fileExtension)) {
      return NextResponse.json(
        { error: "Only MP4 and MOV files are allowed" },
        { status: 400 }
      );
    }

    // Validate file size (500MB limit)
    if (file.size > 500 * 1024 * 1024) {
      return NextResponse.json(
        { error: "File too large. Maximum size is 500MB" },
        { status: 400 }
      );
    }

    // Check R2 configuration
    if (!process.env.CLOUDFLARE_R2_BUCKET_NAME || !process.env.CLOUDFLARE_R2_TOKEN || !process.env.CLOUDFLARE_R2_PUBLIC_URL) {
      console.error("Missing Cloudflare R2 environment variables");
      return NextResponse.json(
        { error: "R2 configuration missing" },
        { status: 500 }
      );
    }

    const supabase = await createSupabaseServerClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Create unique file path
    const timestamp = Date.now();
    const sanitizedFilename = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const fileKey = `users/${user.id}/${timestamp}-${sanitizedFilename}`;

    // Upload file to R2 using Cloudflare API
    const fileBuffer = await file.arrayBuffer();

    const uploadResponse = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${process.env.CLOUDFLARE_ACCOUNT_ID}/r2/buckets/${process.env.CLOUDFLARE_R2_BUCKET_NAME}/objects/${fileKey}`,
      {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${process.env.CLOUDFLARE_R2_TOKEN}`,
          'Content-Type': file.type
        },
        body: fileBuffer
      }
    );

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      console.error('R2 upload error:', errorText);
      return NextResponse.json(
        { error: "Failed to upload video to R2" },
        { status: 500 }
      );
    }

    const publicUrl = `${process.env.CLOUDFLARE_R2_PUBLIC_URL}/${fileKey}`;

    // Create video record in database
    const { data: video, error: dbError } = await supabase
      .from("videos")
      .insert({
        post_id: postId,
        creator_id: user.id,
        title: file.name.replace(/\.[^/.]+$/, ""), // Remove file extension
        r2_file_key: fileKey,
        r2_public_url: publicUrl,
        file_size: file.size,
        is_free: false
      })
      .select()
      .single();

    if (dbError) {
      console.error("Database error:", dbError);
      return NextResponse.json(
        { error: "Failed to save video record" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      uploadUrl,
      fileKey,
      publicUrl,
      video
    });

  } catch (error) {
    console.error("Error generating upload URL:", error);
    return NextResponse.json(
      { error: "Failed to generate upload URL" },
      { status: 500 }
    );
  }
}
