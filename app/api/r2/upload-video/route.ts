import { NextRequest, NextResponse } from "next/server";
import { createSupabaseServerClient } from "@/lib/supabase/client";

export async function POST(request: NextRequest) {
  try {
    const { filename, postId } = await request.json();

    if (!filename || !postId) {
      return NextResponse.json(
        { error: "Filename and postId are required" },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedExtensions = ['.mp4', '.mov'];
    const fileExtension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
    if (!allowedExtensions.includes(fileExtension)) {
      return NextResponse.json(
        { error: "Only MP4 and MOV files are allowed" },
        { status: 400 }
      );
    }

    // Check R2 configuration
    if (!process.env.CLOUDFLARE_R2_BUCKET_NAME || !process.env.CLOUDFLARE_R2_TOKEN || !process.env.CLOUDFLARE_R2_PUBLIC_URL) {
      console.error("Missing Cloudflare R2 environment variables");
      return NextResponse.json(
        { error: "R2 configuration missing" },
        { status: 500 }
      );
    }

    const supabase = await createSupabaseServerClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Create unique file path
    const timestamp = Date.now();
    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_');
    const fileKey = `users/${user.id}/${timestamp}-${sanitizedFilename}`;

    // Create presigned URL using Cloudflare API
    const presignedResponse = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${process.env.CLOUDFLARE_ACCOUNT_ID}/r2/buckets/${process.env.CLOUDFLARE_R2_BUCKET_NAME}/objects/${fileKey}/presigned-url`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.CLOUDFLARE_R2_TOKEN}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          method: 'PUT',
          expires: 3600 // 1 hour
        })
      }
    );

    if (!presignedResponse.ok) {
      const errorText = await presignedResponse.text();
      console.error('Presigned URL error:', errorText);
      return NextResponse.json(
        { error: "Failed to generate upload URL" },
        { status: 500 }
      );
    }

    const presignedData = await presignedResponse.json();
    const uploadUrl = presignedData.result.url;
    const publicUrl = `${process.env.CLOUDFLARE_R2_PUBLIC_URL}/${fileKey}`;

    // Create video record in database
    const { data: video, error: dbError } = await supabase
      .from("videos")
      .insert({
        post_id: postId,
        creator_id: user.id,
        title: filename.replace(/\.[^/.]+$/, ""), // Remove file extension
        r2_file_key: fileKey,
        r2_public_url: publicUrl,
        is_free: false
      })
      .select()
      .single();

    if (dbError) {
      console.error("Database error:", dbError);
      return NextResponse.json(
        { error: "Failed to save video record" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      uploadUrl,
      fileKey,
      publicUrl,
      video
    });

  } catch (error) {
    console.error("Error generating upload URL:", error);
    return NextResponse.json(
      { error: "Failed to generate upload URL" },
      { status: 500 }
    );
  }
}
