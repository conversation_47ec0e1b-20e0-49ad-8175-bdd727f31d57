import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { stripe, calculatePlatformFee } from '@/lib/stripe'
import Stripe from 'stripe'

export async function POST(request: NextRequest) {
  try {
    const { writerId, paymentType = 'subscription', amount, message } = await request.json()
    
    const supabase = await createSupabaseServerClient()
    
    // Get authenticated user (the payer)
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get writer information
    const { data: writer, error: writerError } = await supabase
      .from('users')
      .select('*')
      .eq('id', writerId)
      .single()

    if (writerError || !writer) {
      return NextResponse.json({ error: 'Writer not found' }, { status: 404 })
    }

    // Check if writer has completed Stripe Connect onboarding
    if (!writer.stripe_account_id || !writer.stripe_onboarding_complete) {
      return NextResponse.json({
        error: 'Writer has not completed payment setup. Please contact the writer.'
      }, { status: 400 })
    }

    // Determine amount
    let totalAmount: number
    if (paymentType === 'subscription') {
      totalAmount = writer.price_monthly || 999 // Default $9.99
    } else if (paymentType === 'donation') {
      totalAmount = amount
      if (!totalAmount || totalAmount < 100) {
        return NextResponse.json({ error: 'Minimum donation is $1.00' }, { status: 400 })
      }
    } else {
      return NextResponse.json({ error: 'Invalid payment type' }, { status: 400 })
    }

    // Calculate platform fee (20% for subscriptions, 5% for donations)
    const { platformFee, writerAmount } = calculatePlatformFee(totalAmount, paymentType)

    console.log('Payment setup:', {
      totalAmount,
      paymentType,
      platformFee,
      writerAmount,
      platformFeeInDollars: platformFee / 100,
      writerAmountInDollars: writerAmount / 100
    })

    // Create Stripe Checkout Session
    const sessionConfig: Stripe.Checkout.SessionCreateParams = {
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: paymentType === 'subscription'
                ? `30 Posts from ${writer.name}`
                : `Donation to ${writer.name}`,
              description: paymentType === 'subscription'
                ? 'Access to 30 diary entries'
                : message || 'Thank you for your support!',
            },
            unit_amount: totalAmount,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_SITE_URL}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/u/${writerId}`,
      metadata: {
        payer_id: user.id,
        writer_id: writerId,
        payment_type: paymentType,
      }
    }

    // Add Stripe Connect payment configuration
    sessionConfig.payment_intent_data = {
      application_fee_amount: platformFee,
      transfer_data: {
        destination: writer.stripe_account_id,
      },
      metadata: {
        payer_id: user.id,
        writer_id: writerId,
        payment_type: paymentType,
        product_type: paymentType, // Add this for Stripe pricing rules
        platform_fee: platformFee.toString(),
        writer_amount: writerAmount.toString(),
        ...(message && { donation_message: message })
      }
    }

    console.log('Stripe session config:', {
      application_fee_amount: platformFee,
      destination: writer.stripe_account_id,
      total_amount: totalAmount
    })

    const session = await stripe.checkout.sessions.create(sessionConfig)

    return NextResponse.json({ url: session.url })

  } catch (error) {
    console.error('Checkout error:', error)
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    )
  }
}
