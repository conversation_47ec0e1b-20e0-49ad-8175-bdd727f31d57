import { createSupabaseServerClient } from "@/lib/supabase/client"
import { notFound } from "next/navigation"
import { DiaryInteractions } from "@/components/DiaryInteractions"
import { CommentsSection } from "@/components/CommentsSection"
import { LinkButton } from "@/components/ui/link-button"
import { LoveButton } from "@/components/LoveButton"
import { StructuredData } from "@/components/StructuredData"
import { ImmersiveReader } from "@/components/ImmersiveReader"
import { VideoPlayer } from "@/components/VideoPlayer"
import { ShareButton } from "@/components/ShareButton"
import { getSubscriptionStatus } from "@/lib/paywall"

interface DiaryPageProps {
  params: Promise<{
    id: string
  }>
}

// function formatPrice(cents: number) {
//   return `$${(cents / 100).toFixed(2)}`
// }

// function formatDate(dateString: string) {
//   return new Date(dateString).toLocaleDateString('en-US', {
//     year: 'numeric',
//     month: 'long',
//     day: 'numeric'
//   })
// }

// function formatDateTime(dateString: string) {
//   return new Date(dateString).toLocaleString('en-US', {
//     year: 'numeric',
//     month: 'long',
//     day: 'numeric',
//     hour: 'numeric',
//     minute: '2-digit',
//     hour12: true
//   })
// }

// function getTeaserText(bodyMd: string, maxLength: number = 120) {
//   const plainText = bodyMd.replace(/[#*`_~\[\]()]/g, '').trim()
//   if (plainText.length <= maxLength) return plainText
//   return plainText.slice(0, maxLength) + '...'
// }

export default async function DiaryPage({ params }: DiaryPageProps) {
  const { id } = await params
  const supabase = await createSupabaseServerClient()
  
  // Get the current user (if any)
  const { data: { user } } = await supabase.auth.getUser()
  
  // First try to get the entry using the RPC (for published entries)
  let { data: entryData } = await supabase
    .rpc('get_entry_preview', {
      entry_id: id,
      viewer_id: user?.id
    })

  // If no data found and user is logged in, check if it's their own hidden entry
  if ((!entryData || entryData.length === 0) && user) {
    const { data: hiddenEntry, error: hiddenError } = await supabase
      .from('diary_entries')
      .select(`
        id,
        title,
        body_md,
        is_free,
        is_hidden,
        created_at,
        updated_at,
        user_id,
        users!inner (
          id,
          name,
          custom_url,
          price_monthly
        )
      `)
      .eq('id', id)
      .eq('user_id', user.id)
      .eq('is_hidden', true)
      .single()

    if (hiddenEntry && !hiddenError) {
      // Check if users data exists - handle both array and object formats
      let userData = {}
      if (hiddenEntry.users) {
        if (Array.isArray(hiddenEntry.users) && hiddenEntry.users.length > 0) {
          userData = hiddenEntry.users[0]
        } else if (!Array.isArray(hiddenEntry.users)) {
          userData = hiddenEntry.users
        }
      }

      // Transform to match the RPC response format
      entryData = [{
        id: hiddenEntry.id,
        title: hiddenEntry.title,
        body_md: hiddenEntry.body_md,
        is_free: hiddenEntry.is_free,
        is_hidden: hiddenEntry.is_hidden,
        created_at: hiddenEntry.created_at,
        updated_at: hiddenEntry.updated_at,
        user_id: hiddenEntry.user_id,
        writer_id: hiddenEntry.user_id,
        writer_name: (userData as any)?.name || 'Unknown Writer',
        writer_custom_url: (userData as any)?.custom_url || null,
        writer_price: (userData as any)?.price_monthly || 999,
        can_read_full: true // Writer can always read their own content
      }]
    }
  }

  if (!entryData || entryData.length === 0) {
    notFound()
  }
  
  const entry = entryData[0]

  // Check subscription status for paywall
  const hasSubscription = user ? await getSubscriptionStatus(
    supabase,
    user.id,
    entry.writer_id
  ) : false

  const isOwner = user?.id === entry.writer_id
  const hasAccess = entry.is_free || hasSubscription || isOwner
  const canReadFull = entry.can_read_full || hasAccess

  // Get photos for this entry
  const { data: photos } = await supabase
    .from('photos')
    .select('id, url, alt_text')
    .eq('diary_entry_id', id)
    .eq('moderation_status', 'approved')
    .order('created_at', { ascending: true })

  // Get videos for this entry
  const { data: videos } = await supabase
    .from('videos')
    .select('id, r2_file_key, r2_public_url, title, is_free, view_count, file_size')
    .eq('post_id', id)
    .order('created_at', { ascending: true })



  return (
    <>
      <StructuredData
        type="article"
        data={{
          id: entry.id,
          title: entry.title,
          content: entry.body_md,
          created_at: entry.created_at,
          updated_at: entry.updated_at,
          author: {
            id: entry.writer_id,
            name: entry.writer_name,
            custom_url: entry.writer_custom_url
          },
          photos: photos || undefined
        }}
      />
      <ImmersiveReader
        entry={entry}
        user={user ? { id: user.id, role: user.role ?? '' } : null}
        canReadFull={canReadFull}
      >
        {/* Photos */}
        {photos && photos.length > 0 && canReadFull && (
          <div className="mb-8 flex justify-center">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl">
              {photos.map((photo) => (
                <div key={photo.id} className="relative">
                  <img
                    src={photo.url}
                    alt={photo.alt_text}
                    className="rounded-lg object-cover w-full h-auto"
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Videos */}
        {videos && videos.length > 0 && (
          <div className="mb-8 space-y-6">
            {videos.map((video) => (
              <VideoPlayer
                key={video.id}
                videoId={video.id}
                r2PublicUrl={video.r2_public_url}
                title={video.title}
                hasAccess={canReadFull || video.is_free}
                writerName={entry.writer_name}
                writerId={entry.writer_id}
                initialViewCount={video.view_count || 0}
              />
            ))}
          </div>
        )}

        {/* Love and Share Buttons */}
        {canReadFull && (
          <div className="mb-6 flex items-center gap-4">
            <LoveButton
              entryId={entry.id}
              initialLoveCount={entry.love_count || 0}
              userId={user?.id}
            />
            <ShareButton
              title={entry.title}
              writerName={entry.writer_name}
            />
          </div>
        )}

        {/* Interactive Elements */}
        <DiaryInteractions
          canReadFull={canReadFull}
          entryId={entry.id}
          writerId={entry.writer_id}
        />

        {/* Comments Section */}
        {canReadFull && (
          <CommentsSection
            entryId={entry.id}
            canComment={canReadFull && !!user}
            userId={user?.id}
          />
        )}

        {/* Login Prompt for Visitors */}
        {!user && (
          <div className="bg-white rounded-lg p-6 shadow-sm text-center">
            <p className="text-gray-600 font-serif mb-4">
              Sign in or create an account to subscribe and unlock all content
            </p>
            <div className="flex gap-3 justify-center">
              <LinkButton href="/login" variant="outline">
                Sign In
              </LinkButton>
              <LinkButton href="/register" className="bg-gray-800 text-white hover:bg-gray-700">
                Create Account
              </LinkButton>
            </div>
          </div>
        )}
      </ImmersiveReader>
    </>
  )
}
