'use client'

import { useEffect, useState, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'

function PaymentSuccessContent() {
  const [loading, setLoading] = useState(true)
  const [, setPaymentDetails] = useState<Record<string, unknown> | null>(null)
  const [error, setError] = useState('')
  const searchParams = useSearchParams()
  const sessionId = searchParams.get('session_id')

  useEffect(() => {
    const fetchPaymentDetails = async () => {
      if (!sessionId) {
        setError('No payment session found')
        setLoading(false)
        return
      }

      try {
        // In a real implementation, you'd verify the session with <PERSON><PERSON>
        // For now, we'll just show a success message
        setPaymentDetails({
          type: 'donation',
          amount: 'Your donation',
          status: 'succeeded'
        })
      } catch {
        setError('Failed to verify payment')
      } finally {
        setLoading(false)
      }
    }

    fetchPaymentDetails()
  }, [sessionId])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-gray-600 font-serif">Verifying payment...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-2xl">❌</span>
          </div>
          <h1 className="text-2xl font-serif text-gray-800 mb-4">Payment Error</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link
            href="/"
            className="bg-gray-800 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors"
          >
            Return Home
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
      <div className="max-w-md mx-auto text-center p-8">
        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-3xl">✅</span>
        </div>
        
        <h1 className="text-3xl font-serif text-gray-800 mb-4">
          Thank You! 💝
        </h1>
        
        <p className="text-lg text-gray-600 mb-6">
          Your donation has been processed successfully. The writer will receive 80% of your contribution.
        </p>
        
        <div className="bg-white rounded-lg p-6 shadow-sm mb-6 border border-gray-100">
          <h3 className="font-serif text-lg text-gray-800 mb-3">What happens next?</h3>
          <div className="space-y-2 text-sm text-gray-600 text-left">
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-400 rounded-full"></span>
              <span>Writer receives 80% of your donation</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-400 rounded-full"></span>
              <span>Funds are available for instant withdrawal</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-400 rounded-full"></span>
              <span>Your message (if any) is shared with the writer</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-400 rounded-full"></span>
              <span>You&apos;ll receive an email receipt from Stripe</span>
            </div>
          </div>
        </div>
        
        <div className="space-y-3">
          <Link
            href="/trending"
            className="block w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
          >
            Discover More Stories
          </Link>
          
          <Link
            href="/"
            className="block w-full bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors"
          >
            Return Home
          </Link>
        </div>
        
        <p className="text-xs text-gray-500 mt-6">
          Thank you for supporting independent writers on OnlyDiary!
        </p>
      </div>
    </div>
  )
}

export default function PaymentSuccessPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center h-screen">Loading...</div>}>
      <PaymentSuccessContent />
    </Suspense>
  )
}
