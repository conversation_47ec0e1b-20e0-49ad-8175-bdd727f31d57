"use client"

import { useState } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"

interface PinPostButtonProps {
  entryId: string
  isPinned: boolean
  isOwner: boolean
  onPinChange?: (isPinned: boolean) => void
}

export function PinPostButton({ entryId, isPinned, isOwner, onPinChange }: PinPostButtonProps) {
  const [loading, setLoading] = useState(false)
  const [pinned, setPinned] = useState(isPinned)
  const supabase = createSupabaseClient()

  if (!isOwner) return null

  const handleTogglePin = async () => {
    setLoading(true)
    
    try {
      const newPinnedState = !pinned

      // If pinning this post, unpin any other pinned posts by this user first
      if (newPinnedState) {
        const { data: user } = await supabase.auth.getUser()
        if (user.user) {
          await supabase
            .from('diary_entries')
            .update({ is_pinned: false })
            .eq('writer_id', user.user.id)
            .eq('is_pinned', true)
        }
      }

      // Update this post's pin status
      const { error } = await supabase
        .from('diary_entries')
        .update({ is_pinned: newPinnedState })
        .eq('id', entryId)

      if (error) {
        throw error
      }

      setPinned(newPinnedState)
      onPinChange?.(newPinnedState)

      // Show success message
      if (newPinnedState) {
        alert('✅ Post pinned! This will appear at the top of your diary.')
      } else {
        alert('📌 Post unpinned.')
      }

    } catch (error) {
      console.error('Error toggling pin:', error)
      alert('Failed to update pin status. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <button
      onClick={handleTogglePin}
      disabled={loading}
      className={`flex items-center gap-2 px-3 py-1.5 text-sm rounded-lg transition-colors disabled:opacity-50 ${
        pinned
          ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
      }`}
      title={pinned ? 'Unpin from top' : 'Pin to top of diary'}
    >
      <svg 
        className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} 
        fill="currentColor" 
        viewBox="0 0 20 20"
      >
        {loading ? (
          <path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm6 2.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11z" />
        ) : pinned ? (
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" />
        ) : (
          <path fillRule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
        )}
      </svg>
      <span className="font-medium">
        {loading ? 'Updating...' : pinned ? 'Pinned' : 'Pin to Top'}
      </span>
    </button>
  )
}
