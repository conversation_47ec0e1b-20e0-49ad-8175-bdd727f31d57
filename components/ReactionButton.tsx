"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"

interface Reaction {
  id: string
  emoji: string
  label: string
  count: number
  userReacted: boolean
}

interface ReactionButtonProps {
  entryId: string
  userId?: string
  initialReactions?: Record<string, number>
  userReaction?: string | null
}

const REACTION_TYPES = [
  { id: 'love', emoji: '❤️', label: 'Love' },
  { id: 'fire', emoji: '🔥', label: 'Fire' },
  { id: 'smile', emoji: '😊', label: 'Happy' },
  { id: 'cry', emoji: '😢', label: 'Sad' },
  { id: 'broken', emoji: '💔', label: 'Heartbroken' }
]

export function ReactionButton({ entryId, userId, initialReactions = {}, userReaction }: ReactionButtonProps) {
  const [reactions, setReactions] = useState<Reaction[]>(() =>
    REACTION_TYPES.map(type => ({
      ...type,
      count: initialReactions[type.id] || 0,
      userReacted: userReaction === type.id
    }))
  )
  const [showPicker, setShowPicker] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const supabase = createSupabaseClient()

  const totalReactions = reactions.reduce((sum, reaction) => sum + reaction.count, 0)
  const userCurrentReaction = reactions.find(r => r.userReacted)

  const handleReaction = async (reactionId: string) => {
    if (!userId) {
      alert('Please log in to react')
      return
    }

    setIsLoading(true)
    setShowPicker(false)

    try {
      // If user already has this reaction, remove it
      if (userCurrentReaction?.id === reactionId) {
        await supabase
          .from('reactions')
          .delete()
          .eq('diary_entry_id', entryId)
          .eq('user_id', userId)

        setReactions(prev => prev.map(r => 
          r.id === reactionId 
            ? { ...r, count: Math.max(0, r.count - 1), userReacted: false }
            : r
        ))
      } else {
        // Remove old reaction if exists
        if (userCurrentReaction) {
          await supabase
            .from('reactions')
            .delete()
            .eq('diary_entry_id', entryId)
            .eq('user_id', userId)
        }

        // Add new reaction
        await supabase
          .from('reactions')
          .insert({
            diary_entry_id: entryId,
            user_id: userId,
            reaction_type: reactionId
          })

        setReactions(prev => prev.map(r => ({
          ...r,
          count: r.id === reactionId 
            ? r.count + 1 
            : (r.id === userCurrentReaction?.id ? Math.max(0, r.count - 1) : r.count),
          userReacted: r.id === reactionId
        })))
      }
    } catch (error) {
      console.error('Error updating reaction:', error)
      alert('Failed to update reaction')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="relative">
      {/* Main Button */}
      <button
        onClick={() => setShowPicker(!showPicker)}
        disabled={isLoading}
        className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
      >
        <span className="text-lg">
          {userCurrentReaction ? userCurrentReaction.emoji : '❤️'}
        </span>
        <span className="font-medium">
          {totalReactions > 0 ? totalReactions.toLocaleString() : 'React'}
        </span>
      </button>

      {/* Reaction Picker */}
      {showPicker && (
        <div className="absolute bottom-full left-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg p-2 flex gap-1 z-10">
          {REACTION_TYPES.map((reaction) => (
            <button
              key={reaction.id}
              onClick={() => handleReaction(reaction.id)}
              className={`flex flex-col items-center p-2 rounded-lg hover:bg-gray-100 transition-colors min-w-[60px] ${
                reactions.find(r => r.id === reaction.id)?.userReacted 
                  ? 'bg-blue-50 border border-blue-200' 
                  : ''
              }`}
              title={reaction.label}
            >
              <span className="text-xl mb-1">{reaction.emoji}</span>
              <span className="text-xs text-gray-600 font-medium">
                {reactions.find(r => r.id === reaction.id)?.count || 0}
              </span>
            </button>
          ))}
        </div>
      )}

      {/* Click outside to close */}
      {showPicker && (
        <div 
          className="fixed inset-0 z-0" 
          onClick={() => setShowPicker(false)}
        />
      )}
    </div>
  )
}
