"use client"

import { useEffect, useRef, useState } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"

interface VideoPlayerProps {
  videoId: string
  bunnyVideoId: string
  title?: string
  hasAccess: boolean
  writerName: string
  writerId: string
}

export function VideoPlayer({ 
  videoId, 
  bunnyVideoId, 
  title, 
  hasAccess, 
  writerName, 
  writerId 
}: VideoPlayerProps) {
  useEffect(() => {
    console.log("VideoPlayer props:", { videoId, bunnyVideoId, title, hasAccess, writerName, writerId });
    console.log("Playback URL:", process.env.NEXT_PUBLIC_BUNNY_PLAYBACK_URL);
    console.log("Full video URL:", `${process.env.NEXT_PUBLIC_BUNNY_PLAYBACK_URL}/${bunnyVideoId}/play_720p.mp4`);
  }, [videoId, bunnyVideoId, title, hasAccess, writerName, writerId]);

  const videoRef = useRef<HTMLVideoElement>(null)
  const [viewCounted, setViewCounted] = useState(false)
  const supabase = createSupabaseClient()

  // Track video views
  const handlePlay = async () => {
    if (!viewCounted && hasAccess) {
      try {
        // Increment view_count atomically
        // supabase.sql helper not typed, so cast and ignore TS
        // @ts-ignore
        await (supabase as any)
          .from('videos')
          // @ts-ignore
          .update({ view_count: (supabase as any).sql`view_count + 1` })
          .eq('id', videoId)
        
        setViewCounted(true)
      } catch (error) {
        console.error('Failed to track video view:', error)
      }
    }
  }

  if (!hasAccess) {
    return (
      <div className="relative bg-black rounded-lg overflow-hidden" style={{ aspectRatio: '16/9' }}>
        {/* Blurred Preview */}
        <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="text-6xl mb-4">🎥</div>
            <h3 className="text-xl font-semibold mb-2">Video Content Locked</h3>
            <p className="text-gray-300 mb-4">Subscribe to watch this video</p>
            <button
              onClick={() => {
                // Trigger subscription (same as photo overlay)
                const subscribeButton = document.querySelector('[data-subscribe-button]') as HTMLButtonElement
                if (subscribeButton) {
                  subscribeButton.click()
                }
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Subscribe to {writerName}
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {title && (
        <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
      )}
      
      <div className="relative bg-black rounded-lg overflow-hidden" style={{ aspectRatio: '16/9' }}>
        <video
          controls
          className="w-full h-full object-cover"
          preload="metadata"
          style={{ aspectRatio: '16/9' }}
        >
          <source
            src={`https://vz-b958e519-019.b-cdn.net/${bunnyVideoId}/play.mp4`}
            type="video/mp4"
          />
          <source
            src={`https://vz-b958e519-019.b-cdn.net/${bunnyVideoId}/playlist.m3u8`}
            type="application/x-mpegURL"
          />
          Your browser doesn't support video playback.
        </video>
      </div>

      <div className="flex items-center justify-between text-sm text-gray-500">
        <span>Video by {writerName}</span>
        <span>🎥 Video Content</span>
      </div>
    </div>
  )
}
