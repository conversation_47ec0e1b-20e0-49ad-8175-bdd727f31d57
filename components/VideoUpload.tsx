import { useState } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"

interface VideoUploadProps {
  postId?: string
  onVideoUploaded?: (videoId: string) => void
  onCreateEntry?: () => Promise<string | null>
}

export function VideoUpload({ postId, onVideoUploaded, onCreateEntry }: VideoUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [uploadedVideo, setUploadedVideo] = useState<{
    id: string
    r2PublicUrl: string
    title: string
  } | null>(null)
  const [processing, setProcessing] = useState(false)
  const [localPreviewUrl, setLocalPreviewUrl] = useState<string | null>(null)
  const [uploadSuccess, setUploadSuccess] = useState(false)
  const supabase = createSupabaseClient()







  const handleVideoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Create local preview immediately
    const previewUrl = URL.createObjectURL(file)
    setLocalPreviewUrl(previewUrl)

    // Validate file type
    if (!file.type.startsWith('video/')) {
      setError('Please select a valid video file')
      return
    }

    // Validate file size (100MB limit)
    if (file.size > 100 * 1024 * 1024) {
      setError('Video file must be less than 100MB')
      return
    }

    console.log('🎥 Starting video upload:', file.name)
    setUploading(true)
    setError(null)
    setUploadProgress(10) // Show immediate progress

    try {
      let currentPostId = postId

      // If no entry exists, create one with current content
      if (!currentPostId && onCreateEntry) {
        const savedEntryId = await onCreateEntry()
        if (!savedEntryId) {
          throw new Error('Failed to save entry for video upload')
        }
        currentPostId = savedEntryId
      } else if (!currentPostId) {
        throw new Error('Unable to save entry for video upload')
      }

      // Delete any existing videos for this entry first
      if (currentPostId) {
        await supabase
          .from('videos')
          .delete()
          .eq('post_id', currentPostId)
      }

      // Step 1: Get signed upload URL from R2
      const createResponse = await fetch('/api/r2/upload-video', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          filename: file.name,
          postId: currentPostId
        })
      })

      if (!createResponse.ok) {
        const errorData = await createResponse.json()
        throw new Error(errorData.error || 'Failed to create video')
      }

      const { uploadUrl, publicUrl, video } = await createResponse.json()

      // Step 2: Upload video file directly to R2
      setUploadProgress(50) // Show progress

      const uploadResponse = await fetch(uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type
        }
      })

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload video file')
      }

      // Step 3: Update video record with file size
      await fetch('/api/r2/update-video', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          videoId: video.id,
          fileSize: file.size
        })
      })

      setUploadProgress(100)

      // Set uploaded video for preview
      const videoData = {
        id: video.id,
        r2PublicUrl: publicUrl,
        title: file.name.replace(/\.[^/.]+$/, "")
      }

      console.log('🎥 Video upload completed:', videoData)
      setUploadedVideo(videoData)
      setUploadSuccess(true)
      setProcessing(false) // Don't show processing - video is ready to use
      onVideoUploaded?.(video.id)

      // Reset form
      event.target.value = ''
      
    } catch (err) {
      console.error('Video upload error:', err)
      setError(err instanceof Error ? err.message : 'Upload failed')
    } finally {
      setUploading(false)
      setTimeout(() => setUploadProgress(0), 2000)
    }
  }

  return (
    <div className="border-t border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Videos</h3>
        {uploadedVideo && (
          <span className="text-sm text-gray-500">
            {processing ? "Processing..." : "Ready"}
          </span>
        )}
      </div>

      {/* Upload Button */}
      <div className="mb-4">
        <input
          type="file"
          accept="video/*"
          onChange={handleVideoUpload}
          disabled={uploading || processing}
          className="hidden"
          id="video-upload"
        />
        <button
          onClick={() => document.getElementById('video-upload')?.click()}
          disabled={uploading || processing}
          className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {uploading ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
              <span>Uploading...</span>
            </div>
          ) : processing ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <span>Processing...</span>
            </div>
          ) : (
            "Add Video"
          )}
        </button>
      </div>

      {/* Upload Progress */}
      {uploading && (
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-600 text-sm font-medium mb-3">
            📤 Uploading video...
          </p>
          <div className="w-full bg-blue-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
          <p className="text-xs text-blue-500 mt-2">{uploadProgress}% complete</p>
        </div>
      )}

      {/* Success Message */}
      {uploadSuccess && !uploading && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-green-600 text-sm font-medium mb-2">
            ✅ Video uploaded successfully!
          </p>
          <p className="text-xs text-green-500">
            Your video is ready to view and your post can be published.
          </p>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Video Preview */}
      {(localPreviewUrl || uploadedVideo) && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Video Preview</h4>
          <div className="bg-black rounded-lg overflow-hidden" style={{ aspectRatio: '16/9' }}>
            {localPreviewUrl ? (
              // Show local preview
              <video
                controls
                className="w-full h-full"
                style={{ aspectRatio: '16/9' }}
                src={localPreviewUrl}
              />
            ) : uploadedVideo ? (
              // Show R2 or Bunny.net video when uploaded
              <video
                controls
                className="w-full h-full"
                style={{ aspectRatio: '16/9' }}
              >
                <source src={uploadedVideo.r2PublicUrl} type="video/mp4" />
                <p className="text-white p-4">Video uploaded successfully!</p>
              </video>
            ) : null}
          </div>
          <div className="mt-2 flex items-center justify-between text-sm text-gray-500">
            <span>{uploadedVideo?.title ?? 'Selected video'}</span>
            <button
              onClick={() => {
                setUploadedVideo(null)
                setProcessing(false)
                setUploadSuccess(false)
                setLocalPreviewUrl(null)
                if (localPreviewUrl) {
                  URL.revokeObjectURL(localPreviewUrl)
                }
              }}
              className="text-red-600 hover:text-red-700"
            >
              Remove
            </button>
          </div>
        </div>
      )}

      <p className="text-xs text-gray-500">
        Supported formats: MP4, MOV, AVI • Max size: 100MB
      </p>
    </div>
  )
}
