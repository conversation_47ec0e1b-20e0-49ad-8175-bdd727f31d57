"use client"

interface VideoWatermarkProps {
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  opacity?: number
  size?: 'small' | 'medium' | 'large'
}

export function VideoWatermark({ 
  position = 'bottom-right', 
  opacity = 0.8,
  size = 'medium' 
}: VideoWatermarkProps) {
  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  }

  const sizeClasses = {
    'small': 'text-xs px-2 py-1',
    'medium': 'text-sm px-3 py-1.5',
    'large': 'text-base px-4 py-2'
  }

  return (
    <div 
      className={`absolute ${positionClasses[position]} z-10 pointer-events-none select-none`}
      style={{ opacity }}
    >
      {/* Main Watermark */}
      <div className="flex items-center gap-2 bg-black/60 backdrop-blur-sm rounded-lg px-3 py-1.5 border border-white/20">
        {/* Logo/Icon */}
        <div className="flex items-center justify-center w-6 h-6 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full shadow-lg">
          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 22 12 18.27 5.82 22 7 13.87 2 9l6.91-.74L12 2z" />
          </svg>
        </div>
        
        {/* Text */}
        <div className="flex flex-col leading-none">
          <span className="text-white/90 font-bold text-xs tracking-wide">
            Only on
          </span>
          <span className="text-white font-black text-sm tracking-wider">
            OnlyDiary.app
          </span>
        </div>
      </div>
      
      {/* Subtle glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg blur-sm -z-10" />
    </div>
  )
}

// Alternative compact version
export function VideoWatermarkCompact({ 
  position = 'bottom-right', 
  opacity = 0.7 
}: Pick<VideoWatermarkProps, 'position' | 'opacity'>) {
  const positionClasses = {
    'top-left': 'top-3 left-3',
    'top-right': 'top-3 right-3',
    'bottom-left': 'bottom-3 left-3',
    'bottom-right': 'bottom-3 right-3'
  }

  return (
    <div 
      className={`absolute ${positionClasses[position]} z-10 pointer-events-none select-none`}
      style={{ opacity }}
    >
      <div className="bg-black/50 backdrop-blur-sm rounded-full px-3 py-1 border border-white/10">
        <span className="text-white font-bold text-xs tracking-wide">
          OnlyDiary.app
        </span>
      </div>
    </div>
  )
}

// Premium version with animation
export function VideoWatermarkPremium({ 
  position = 'bottom-right', 
  opacity = 0.8 
}: Pick<VideoWatermarkProps, 'position' | 'opacity'>) {
  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  }

  return (
    <div 
      className={`absolute ${positionClasses[position]} z-10 pointer-events-none select-none`}
      style={{ opacity }}
    >
      <div className="relative">
        {/* Animated border */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500 via-pink-500 to-purple-500 rounded-lg p-[1px] animate-pulse">
          <div className="bg-black/80 rounded-lg w-full h-full" />
        </div>
        
        {/* Content */}
        <div className="relative flex items-center gap-2 px-3 py-2 bg-black/80 backdrop-blur-sm rounded-lg">
          {/* Animated logo */}
          <div className="flex items-center justify-center w-7 h-7 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full animate-pulse">
            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          
          <div className="flex flex-col leading-none">
            <span className="text-white/90 font-semibold text-xs tracking-wide">
              Exclusively on
            </span>
            <span className="text-white font-black text-sm tracking-wider">
              <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-purple-400 bg-clip-text text-transparent animate-pulse">
                OnlyDiary.app
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
