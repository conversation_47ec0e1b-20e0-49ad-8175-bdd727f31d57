import { S3Client } from '@aws-sdk/client-s3';

// Create R2 client using S3-compatible API
const token = process.env.CLOUDFLARE_R2_TOKEN || '';

const r2Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_S3_ENDPOINT,
  credentials: {
    // Use first 32 characters as access key, full token as secret
    accessKeyId: token.substring(0, 32),
    secretAccessKey: token
  }
});

export default r2Client;
