/* OnlyDiary Video Watermark Styles */

.onlydiary-video-watermark {
  position: absolute;
  bottom: 16px;
  right: 16px;
  z-index: 1000;
  
  /* Styling */
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px 12px;
  
  /* Typography */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  font-size: 13px;
  font-weight: 700;
  color: white;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  
  /* Effects */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  opacity: 0.85;
  
  /* Prevent interaction */
  pointer-events: none;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.onlydiary-video-watermark::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.15), rgba(236, 72, 153, 0.15));
  border-radius: 8px;
  z-index: -1;
}

/* Alternative positions */
.onlydiary-video-watermark.top-left {
  top: 16px;
  left: 16px;
  bottom: auto;
  right: auto;
}

.onlydiary-video-watermark.top-right {
  top: 16px;
  right: 16px;
  bottom: auto;
  left: auto;
}

.onlydiary-video-watermark.bottom-left {
  bottom: 16px;
  left: 16px;
  top: auto;
  right: auto;
}

.onlydiary-video-watermark.bottom-right {
  bottom: 16px;
  right: 16px;
  top: auto;
  left: auto;
}

/* Size variations */
.onlydiary-video-watermark.small {
  font-size: 11px;
  padding: 6px 8px;
}

.onlydiary-video-watermark.large {
  font-size: 15px;
  padding: 10px 16px;
}

/* Compact version */
.onlydiary-video-watermark.compact {
  background: rgba(0, 0, 0, 0.6);
  border-radius: 20px;
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 600;
}

/* Premium animated version */
.onlydiary-video-watermark.premium {
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid transparent;
  background-clip: padding-box;
  position: relative;
}

.onlydiary-video-watermark.premium::before {
  background: linear-gradient(135deg, #9333ea, #ec4899, #9333ea);
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* For video containers */
.video-container {
  position: relative;
  display: inline-block;
}

.video-container video {
  display: block;
  width: 100%;
  height: auto;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .onlydiary-video-watermark {
    font-size: 11px;
    padding: 6px 10px;
    bottom: 12px;
    right: 12px;
  }
  
  .onlydiary-video-watermark.small {
    font-size: 10px;
    padding: 4px 8px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .onlydiary-video-watermark {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid white;
    color: white;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .onlydiary-video-watermark.premium::before {
    animation: none;
  }
}
