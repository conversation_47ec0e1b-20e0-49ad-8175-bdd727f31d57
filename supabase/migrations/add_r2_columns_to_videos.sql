-- Add R2-specific columns to videos table
ALTER TABLE videos 
ADD COLUMN r2_file_key VARCHAR(255),
ADD COLUMN r2_public_url VARCHAR(500),
ADD COLUMN file_size INTEGER;

-- Add index for better query performance
CREATE INDEX idx_videos_r2_file_key ON videos(r2_file_key);

-- Add comment to document the migration
COMMENT ON COLUMN videos.r2_file_key IS 'Cloudflare R2 object key for the video file';
COMMENT ON COLUMN videos.r2_public_url IS 'Public URL for direct video access via R2';
COMMENT ON COLUMN videos.file_size IS 'Video file size in bytes';
