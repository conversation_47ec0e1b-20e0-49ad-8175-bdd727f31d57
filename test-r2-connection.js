// Simple test script to verify R2 connection
// Run with: node test-r2-connection.js

import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3';

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const r2Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_S3_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_TOKEN?.split(':')[0] || '',
    secretAccessKey: process.env.CLOUDFLARE_R2_TOKEN?.split(':')[1] || ''
  }
});

async function testR2Connection() {
  try {
    console.log('Testing R2 connection...');
    console.log('Endpoint:', process.env.CLOUDFLARE_R2_S3_ENDPOINT);
    console.log('Bucket:', process.env.CLOUDFLARE_R2_BUCKET_NAME);
    
    const command = new ListObjectsV2Command({
      Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
      MaxKeys: 5
    });
    
    const response = await r2Client.send(command);
    console.log('✅ R2 connection successful!');
    console.log('Objects in bucket:', response.Contents?.length || 0);
    
    if (response.Contents && response.Contents.length > 0) {
      console.log('Sample objects:');
      response.Contents.slice(0, 3).forEach(obj => {
        console.log(`  - ${obj.Key} (${obj.Size} bytes)`);
      });
    }
    
  } catch (error) {
    console.error('❌ R2 connection failed:', error.message);
    console.error('Check your environment variables:');
    console.error('- CLOUDFLARE_R2_S3_ENDPOINT');
    console.error('- CLOUDFLARE_R2_BUCKET_NAME');
    console.error('- CLOUDFLARE_R2_TOKEN');
  }
}

testR2Connection();
